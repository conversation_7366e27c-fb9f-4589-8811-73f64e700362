import React, { createContext, useContext, useEffect, useState } from 'react';
import { Models } from 'appwrite';
import { account, ID } from '@/lib/appwrite';

interface AuthContextType {
  user: Models.User<Models.Preferences> | null;
  session: Models.Session | null;
  loading: boolean;
  signOut: () => Promise<void>;
  signInWithEmail: (email: string, password: string) => Promise<void>;
  signUpWithEmail: (email: string, password: string, name?: string) => Promise<void>;
  signInWithOAuth: (provider: string) => Promise<string>;
  resetPassword: (email: string) => Promise<void>;
  refreshSession: () => Promise<boolean>;
  isSessionValid: () => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<Models.User<Models.Preferences> | null>(null);
  const [session, setSession] = useState<Models.Session | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session with a small delay to ensure Appwrite client is ready
    const timer = setTimeout(() => {
      checkSession();
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  const checkSession = async () => {
    try {
      const currentUser = await account.get();
      setUser(currentUser);

      // Get current session
      const currentSession = await account.getSession('current');
      setSession(currentSession);
    } catch (error: any) {
      // Silently handle authentication errors - this is expected when no user is logged in
      if (error?.code === 401 || error?.type === 'general_unauthorized_scope') {
        // User not authenticated - this is normal
        console.log('No active session (expected)');
      } else {
        // Log other errors for debugging
        console.log('Session check error:', error?.message || error);
      }
      setUser(null);
      setSession(null);
    } finally {
      setLoading(false);
    }
  };

  const signInWithEmail = async (email: string, password: string) => {
    try {
      const session = await account.createEmailPasswordSession(email, password);
      setSession(session);

      const user = await account.get();
      setUser(user);
    } catch (error) {
      console.error('Sign in error:', error);
      throw error;
    }
  };

  const signUpWithEmail = async (email: string, password: string, name?: string) => {
    try {
      await account.create(ID.unique(), email, password, name);
      await signInWithEmail(email, password);
    } catch (error) {
      console.error('Sign up error:', error);
      throw error;
    }
  };

  const signInWithOAuth = async (provider: string): Promise<string> => {
    try {
      // This method returns the OAuth URL for the login page to handle
      // The actual session creation is handled in the login page after OAuth callback
      const { OAuthProvider } = await import('appwrite');

      // Map provider string to OAuthProvider enum
      let oauthProvider;
      switch (provider.toLowerCase()) {
        case 'google':
          oauthProvider = OAuthProvider.Google;
          break;
        default:
          throw new Error(`Unsupported OAuth provider: ${provider}`);
      }

      // This is handled in the login page, so we just return a placeholder
      // The actual OAuth flow is managed by the login component
      return 'oauth-handled-by-login-component';
    } catch (error) {
      console.error('OAuth error:', error);
      throw error;
    }
  };

  const resetPassword = async (email: string) => {
    try {
      await account.createRecovery(email, 'plantconnects://reset-password');
    } catch (error) {
      console.error('Password reset error:', error);
      throw error;
    }
  };

  const signOut = async () => {
    try {
      await account.deleteSession('current');
      setUser(null);
      setSession(null);
    } catch (error) {
      console.error('Sign out error:', error);
      throw error;
    }
  };

  const refreshSession = async (): Promise<boolean> => {
    try {
      const currentUser = await account.get();
      const currentSession = await account.getSession('current');

      if (currentUser && currentSession) {
        setUser(currentUser);
        setSession(currentSession);
        return true;
      }

      return false;
    } catch (error) {
      console.error('Session refresh error:', error);
      return false;
    }
  };

  const isSessionValid = (): boolean => {
    if (!session) return false;

    // Check if session is expired
    const expiresAt = new Date(session.expire).getTime();
    const now = Date.now();
    const buffer = 5 * 60 * 1000; // 5 minutes buffer

    return expiresAt > (now + buffer);
  };

  const value = {
    user,
    session,
    loading,
    signOut,
    signInWithEmail,
    signUpWithEmail,
    signInWithOAuth,
    resetPassword,
    refreshSession,
    isSessionValid,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
