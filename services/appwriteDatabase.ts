import { databases, DATABASE_ID, COLLECTIONS, ID } from '@/lib/appwrite';
import { Query } from 'appwrite';
import { Plant } from '@/types/plant';

// Import interfaces from the original database service
export interface UserProfile {
  id: string;
  user_id: string;
  username?: string;
  display_name?: string;
  bio?: string;
  avatar_url?: string;
  location?: string;
  website_url?: string;
  is_public: boolean;
  allow_garden_sharing: boolean;
  allow_profile_indexing: boolean;
  experience_level: 'beginner' | 'intermediate' | 'expert';
  total_identifications: number;
  total_diagnoses: number;
  community_points: number;
  achievements: any[];
  created_at: string;
  updated_at: string;
}

export interface PlantIdentification {
  id: string;
  user_id: string;
  image_url: string;
  scientific_name?: string;
  common_name?: string;
  family_name?: string;
  description?: string;
  care_instructions?: string;
  tags?: string[];
  confidence_score?: number;
  identification_source: string;
  is_verified: boolean;
  verified_by?: string;
  verified_at?: string;
  is_public: boolean;
  location_taken?: string;
  weather_conditions?: string;
  created_at: string;
  updated_at: string;
  seo_title?: string;
  seo_description?: string;
  seo_keywords?: string[];
  slug?: string;
  plant_type?: string;
  native_region?: string;
  toxicity_level?: string;
  toxicity_warning?: string;
  growth_habit?: string;
  growth_rate?: string;
  mature_height?: string;
  mature_width?: string;
  mature_description?: string;
  bloom_time?: string;
  flower_colors?: string[];
  foliage_type?: string;
  hardiness_zones?: string;
  min_temperature?: string;
  pests_and_diseases?: string;
  fun_facts?: string;
  uses?: string[];
  propagation?: string;
  seasonal_care?: string;
  companion_plants?: string[];
  maintenance_level?: string;
  // Garden fields (moved from garden_collections)
  notes?: string;
  nickname?: string;
  health_status?: 'healthy' | 'sick' | 'recovering' | 'critical';
  location_in_garden?: string;
  date_acquired?: string;
  last_watered?: string;
  last_fertilized?: string;
  last_repotted?: string;
  watering_frequency_days?: number;
  fertilizing_frequency_days?: number;
  allow_community_tips?: boolean;
}

export interface PlantDiagnosis {
  id: string;
  user_id: string;
  garden_collection_id?: string;
  plant_identification_id?: string;
  image_url: string;
  problem_description?: string;
  diagnosed_problem?: string;
  likely_causes?: string[];
  symptoms_observed?: string;
  severity?: 'mild' | 'moderate' | 'severe' | 'critical';
  immediate_actions?: string[];
  long_term_care?: string[];
  product_recommendations?: string[];
  step_by_step_instructions?: string[];
  prevention_tips?: string[];
  prognosis?: string;
  confidence_score?: number;
  diagnosis_source: string;
  is_verified: boolean;
  verified_by?: string;
  verified_at?: string;
  is_public: boolean;
  notes?: string;
  location?: string;
  nickname?: string;
  health_status?: 'healthy' | 'sick' | 'recovering' | 'critical';
  location_in_garden?: string;
  date_acquired?: string;
  last_watered?: string;
  last_fertilized?: string;
  last_repotted?: string;
  watering_frequency_days?: number;
  fertilizing_frequency_days?: number;
  allow_community_tips?: boolean;
  scientific_name?: string;
  common_name?: string;
  description?: string;
  plant_type?: string;
  native_region?: string;
  toxicity_level?: string;
  toxicity_warning?: string;
  growth_habit?: string;
  growth_rate?: string;
  mature_height?: string;
  mature_width?: string;
  mature_description?: string;
  bloom_time?: string;
  flower_colors?: string[];
  foliage_type?: string;
  hardiness_zones?: string;
  min_temperature?: string;
  pests_and_diseases?: string;
  fun_facts?: string[];
  uses?: string[];
  propagation?: string;
  seasonal_care?: string;
  companion_plants?: string[];
  maintenance_level?: string;
  tags?: string[];
  seo_title?: string;
  seo_description?: string;
  seo_keywords?: string[];
  slug?: string;
  care_instructions?: string;
  created_at: string;
  updated_at: string;
}

export interface GardenCollection {
  id: string;
  user_id: string;
  plant_identification_id?: string;
  nickname?: string;
  notes?: string;
  health_status: 'healthy' | 'sick' | 'recovering' | 'critical';
  location_in_garden?: string;
  date_acquired?: string;
  last_watered?: string;
  last_fertilized?: string;
  last_repotted?: string;
  watering_frequency_days?: number;
  fertilizing_frequency_days?: number;
  is_public: boolean;
  allow_community_tips: boolean;
  created_at: string;
  updated_at: string;
  seo_title?: string;
  seo_description?: string;
  seo_keywords?: string[];
  slug?: string;
}

export interface RecoveryTracking {
  id: string;
  diagnosis_id: string;
  user_id: string;
  progress_image_url?: string;
  recovery_status: 'in_progress' | 'improving' | 'recovered' | 'worsening' | 'failed';
  progress_notes?: string;
  treatments_applied?: string[];
  date_applied: string;
  effectiveness_rating?: number;
  side_effects?: string;
  next_steps?: string;
  is_public: boolean;
  created_at: string;
  updated_at: string;
}

export class AppwriteDatabaseService {
  // Helper methods
  private static sanitizeString(value: any): string | undefined {
    if (typeof value === 'string') return value.trim() || undefined;
    return undefined;
  }

  private static sanitizeArray(value: any): string[] | undefined {
    if (Array.isArray(value)) return value.filter(item => typeof item === 'string' && item.trim());
    return undefined;
  }

  // User Profile methods
  static async getUserProfile(userId: string): Promise<UserProfile | null> {
    try {
      const response = await databases.getDocument(
        DATABASE_ID,
        COLLECTIONS.USER_PROFILES,
        userId
      );
      return response as UserProfile;
    } catch (error: any) {
      if (error.code === 404) {
        return null; // Profile not found
      }
      console.error('Error fetching user profile:', error);
      throw new Error(`Failed to load profile: ${error.message}`);
    }
  }

  static async createUserProfile(profile: Omit<UserProfile, 'id' | 'created_at' | 'updated_at'>): Promise<UserProfile | null> {
    try {
      const now = new Date().toISOString();
      const response = await databases.createDocument(
        DATABASE_ID,
        COLLECTIONS.USER_PROFILES,
        profile.user_id, // Use user_id as document ID
        {
          ...profile,
          created_at: now,
          updated_at: now,
        }
      );
      return response as UserProfile;
    } catch (error) {
      console.error('Error creating user profile:', error);
      return null;
    }
  }

  static async updateUserProfile(userId: string, updates: Partial<UserProfile>): Promise<UserProfile | null> {
    try {
      const response = await databases.updateDocument(
        DATABASE_ID,
        COLLECTIONS.USER_PROFILES,
        userId,
        {
          ...updates,
          updated_at: new Date().toISOString(),
        }
      );
      return response as UserProfile;
    } catch (error: any) {
      console.error('Error updating user profile:', error);
      if (error.code === 404) {
        throw new Error('Profile not found. Please try logging out and back in.');
      }
      throw new Error(`Failed to update profile: ${error.message}`);
    }
  }

  static async updateUserProfileStats(userId: string): Promise<UserProfile | null> {
    try {
      // Get current counts from database
      const [identificationsResult, diagnosesResult] = await Promise.all([
        databases.listDocuments(
          DATABASE_ID,
          COLLECTIONS.PLANT_IDENTIFICATIONS,
          [Query.equal('user_id', userId)]
        ),
        databases.listDocuments(
          DATABASE_ID,
          COLLECTIONS.PLANT_DIAGNOSES,
          [Query.equal('user_id', userId)]
        )
      ]);

      const identificationCount = identificationsResult.total;
      const diagnosisCount = diagnosesResult.total;

      // Update user profile with current counts
      return await this.updateUserProfile(userId, {
        total_identifications: identificationCount,
        total_diagnoses: diagnosisCount,
      });
    } catch (error) {
      console.error('Error updating user profile stats:', error);
      return null;
    }
  }

  // Plant Identification methods
  static async createPlantIdentification(identification: Omit<PlantIdentification, 'id' | 'created_at' | 'updated_at'>): Promise<PlantIdentification> {
    try {
      const now = new Date().toISOString();
      const response = await databases.createDocument(
        DATABASE_ID,
        COLLECTIONS.PLANT_IDENTIFICATIONS,
        ID.unique(),
        {
          ...identification,
          created_at: now,
          updated_at: now,
        }
      );
      return response as PlantIdentification;
    } catch (error: any) {
      console.error('Error creating plant identification:', error);
      throw new Error(`Failed to save plant identification: ${error.message}`);
    }
  }

  static async getPlantIdentification(id: string): Promise<PlantIdentification | null> {
    try {
      const response = await databases.getDocument(
        DATABASE_ID,
        COLLECTIONS.PLANT_IDENTIFICATIONS,
        id
      );
      return response as PlantIdentification;
    } catch (error: any) {
      if (error.code === 404) {
        return null;
      }
      console.error('Error fetching plant identification:', error);
      return null;
    }
  }

  static async getPlantIdentifications(userId: string): Promise<PlantIdentification[]> {
    try {
      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.PLANT_IDENTIFICATIONS,
        [
          Query.equal('user_id', userId),
          Query.orderDesc('created_at')
        ]
      );
      return response.documents as PlantIdentification[];
    } catch (error) {
      console.error('Error fetching plant identifications:', error);
      return [];
    }
  }

  static async getRecentPlantIdentifications(userId: string, limit: number = 5): Promise<PlantIdentification[]> {
    try {
      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.PLANT_IDENTIFICATIONS,
        [
          Query.equal('user_id', userId),
          Query.orderDesc('created_at'),
          Query.limit(limit)
        ]
      );
      return response.documents as PlantIdentification[];
    } catch (error) {
      console.error('Error fetching recent plant identifications:', error);
      return [];
    }
  }

  static async updatePlantIdentification(id: string, updates: Partial<PlantIdentification>): Promise<PlantIdentification | null> {
    try {
      const response = await databases.updateDocument(
        DATABASE_ID,
        COLLECTIONS.PLANT_IDENTIFICATIONS,
        id,
        {
          ...updates,
          updated_at: new Date().toISOString(),
        }
      );
      return response as PlantIdentification;
    } catch (error) {
      console.error('Error updating plant identification:', error);
      return null;
    }
  }

  static async removePlantIdentification(id: string): Promise<boolean> {
    try {
      await databases.deleteDocument(
        DATABASE_ID,
        COLLECTIONS.PLANT_IDENTIFICATIONS,
        id
      );
      return true;
    } catch (error) {
      console.error('Error removing plant identification:', error);
      return false;
    }
  }

  // Plant Diagnosis methods
  static async createPlantDiagnosis(diagnosis: Omit<PlantDiagnosis, 'id' | 'created_at' | 'updated_at'>): Promise<PlantDiagnosis> {
    try {
      const now = new Date().toISOString();
      const response = await databases.createDocument(
        DATABASE_ID,
        COLLECTIONS.PLANT_DIAGNOSES,
        ID.unique(),
        {
          ...diagnosis,
          created_at: now,
          updated_at: now,
        }
      );
      return response as PlantDiagnosis;
    } catch (error: any) {
      console.error('Error creating plant diagnosis:', error);
      throw new Error(`Failed to save plant diagnosis: ${error.message}`);
    }
  }

  static async getPlantDiagnosis(id: string): Promise<PlantDiagnosis | null> {
    try {
      const response = await databases.getDocument(
        DATABASE_ID,
        COLLECTIONS.PLANT_DIAGNOSES,
        id
      );
      return response as PlantDiagnosis;
    } catch (error: any) {
      if (error.code === 404) {
        return null;
      }
      console.error('Error fetching plant diagnosis:', error);
      return null;
    }
  }

  static async getPlantDiagnoses(userId: string): Promise<PlantDiagnosis[]> {
    try {
      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.PLANT_DIAGNOSES,
        [
          Query.equal('user_id', userId),
          Query.orderDesc('created_at')
        ]
      );
      return response.documents as PlantDiagnosis[];
    } catch (error) {
      console.error('Error fetching plant diagnoses:', error);
      return [];
    }
  }

  static async getRecentPlantDiagnoses(userId: string, limit: number = 5): Promise<PlantDiagnosis[]> {
    try {
      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.PLANT_DIAGNOSES,
        [
          Query.equal('user_id', userId),
          Query.orderDesc('created_at'),
          Query.limit(limit)
        ]
      );
      return response.documents as PlantDiagnosis[];
    } catch (error) {
      console.error('Error fetching recent plant diagnoses:', error);
      return [];
    }
  }

  static async updatePlantDiagnosis(id: string, updates: Partial<PlantDiagnosis>): Promise<PlantDiagnosis | null> {
    try {
      const response = await databases.updateDocument(
        DATABASE_ID,
        COLLECTIONS.PLANT_DIAGNOSES,
        id,
        {
          ...updates,
          updated_at: new Date().toISOString(),
        }
      );
      return response as PlantDiagnosis;
    } catch (error) {
      console.error('Error updating plant diagnosis:', error);
      return null;
    }
  }

  static async updateDiagnosisNotes(diagnosisId: string, notes: string): Promise<PlantDiagnosis> {
    try {
      const response = await databases.updateDocument(
        DATABASE_ID,
        COLLECTIONS.PLANT_DIAGNOSES,
        diagnosisId,
        {
          notes: notes,
          updated_at: new Date().toISOString(),
        }
      );
      return response as PlantDiagnosis;
    } catch (error: any) {
      console.error('Error updating diagnosis notes:', error);
      throw new Error(`Failed to update diagnosis notes: ${error.message}`);
    }
  }

  // Garden Collection methods
  static async addToGarden(gardenItem: Omit<GardenCollection, 'id' | 'created_at' | 'updated_at'>): Promise<GardenCollection | null> {
    try {
      const now = new Date().toISOString();
      const response = await databases.createDocument(
        DATABASE_ID,
        COLLECTIONS.GARDEN_COLLECTIONS,
        ID.unique(),
        {
          ...gardenItem,
          created_at: now,
          updated_at: now,
        }
      );
      return response as GardenCollection;
    } catch (error) {
      console.error('Error adding to garden:', error);
      return null;
    }
  }

  static async getGardenCollections(userId: string): Promise<GardenCollection[]> {
    try {
      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.GARDEN_COLLECTIONS,
        [
          Query.equal('user_id', userId),
          Query.orderDesc('created_at')
        ]
      );
      return response.documents as GardenCollection[];
    } catch (error) {
      console.error('Error fetching garden collections:', error);
      return [];
    }
  }

  static async updateGardenCollection(id: string, updates: Partial<GardenCollection>): Promise<GardenCollection | null> {
    try {
      const response = await databases.updateDocument(
        DATABASE_ID,
        COLLECTIONS.GARDEN_COLLECTIONS,
        id,
        {
          ...updates,
          updated_at: new Date().toISOString(),
        }
      );
      return response as GardenCollection;
    } catch (error) {
      console.error('Error updating garden collection:', error);
      return null;
    }
  }

  static async removeFromGarden(id: string): Promise<boolean> {
    try {
      await databases.deleteDocument(
        DATABASE_ID,
        COLLECTIONS.GARDEN_COLLECTIONS,
        id
      );
      return true;
    } catch (error) {
      console.error('Error removing from garden:', error);
      return false;
    }
  }

  // Recovery Tracking methods
  static async createRecoveryTracking(tracking: Omit<RecoveryTracking, 'id' | 'created_at' | 'updated_at'>): Promise<RecoveryTracking | null> {
    try {
      const now = new Date().toISOString();
      const response = await databases.createDocument(
        DATABASE_ID,
        COLLECTIONS.RECOVERY_TRACKING,
        ID.unique(),
        {
          ...tracking,
          created_at: now,
          updated_at: now,
        }
      );
      return response as RecoveryTracking;
    } catch (error) {
      console.error('Error creating recovery tracking:', error);
      return null;
    }
  }

  static async getRecoveryTracking(diagnosisId: string): Promise<RecoveryTracking[]> {
    try {
      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTIONS.RECOVERY_TRACKING,
        [
          Query.equal('diagnosis_id', diagnosisId),
          Query.orderDesc('date_applied')
        ]
      );
      return response.documents as RecoveryTracking[];
    } catch (error) {
      console.error('Error fetching recovery tracking:', error);
      return [];
    }
  }

  // Helper methods for creating diagnosis-only records
  static async createDiagnosisOnly(plant: Plant, imageUri: string, userId: string, diagnosisData: any, notes?: string, location?: string): Promise<PlantDiagnosis> {
    const mapSeverityToHealthStatus = (severity?: string): 'healthy' | 'sick' | 'recovering' | 'critical' => {
      switch (severity?.toLowerCase()) {
        case 'mild': return 'sick';
        case 'moderate': return 'sick';
        case 'severe': return 'critical';
        case 'critical': return 'critical';
        default: return 'sick';
      }
    };

    const diagnosis = await this.createPlantDiagnosis({
      user_id: userId,
      image_url: imageUri,
      problem_description: this.sanitizeString(diagnosisData.problemDescription),
      diagnosed_problem: this.sanitizeString(diagnosisData.diagnosedProblem),
      likely_causes: this.sanitizeArray(diagnosisData.likelyCauses),
      symptoms_observed: this.sanitizeString(diagnosisData.symptomsObserved),
      severity: diagnosisData.severity?.toLowerCase() as 'mild' | 'moderate' | 'severe' | 'critical',
      immediate_actions: this.sanitizeArray(diagnosisData.immediateActions),
      long_term_care: this.sanitizeArray(diagnosisData.longTermCare),
      product_recommendations: this.sanitizeArray(diagnosisData.productRecommendations),
      step_by_step_instructions: this.sanitizeArray(diagnosisData.stepByStepInstructions),
      prevention_tips: this.sanitizeArray(diagnosisData.preventionTips),
      prognosis: this.sanitizeString(diagnosisData.prognosis),
      confidence_score: diagnosisData.confidence || 0.95,
      diagnosis_source: 'openrouter_api',
      is_verified: false,
      is_public: false,
      notes: notes,
      location: location,
      nickname: plant.commonName,
      health_status: mapSeverityToHealthStatus(diagnosisData.severity),
      allow_community_tips: false,
      scientific_name: plant.scientificName,
      common_name: plant.commonName,
      description: plant.description,
      care_instructions: JSON.stringify(plant.careInstructions),
      plant_type: plant.plantType,
      native_region: plant.nativeRegion,
      toxicity_level: plant.toxicityLevel,
      toxicity_warning: plant.toxicityWarning,
      growth_habit: plant.growthHabit,
      growth_rate: plant.growthRate,
      mature_height: plant.matureHeight,
      mature_width: plant.matureWidth,
      mature_description: plant.matureDescription,
      bloom_time: plant.bloomTime,
      flower_colors: plant.flowerColors,
      foliage_type: plant.foliageType,
      hardiness_zones: plant.hardinessZones,
      min_temperature: plant.minTemperature,
      pests_and_diseases: plant.pestsAndDiseases,
      fun_facts: plant.funFacts,
      uses: plant.uses,
      propagation: plant.propagation,
      seasonal_care: plant.seasonalCare,
      companion_plants: plant.companionPlants,
      maintenance_level: plant.maintenanceLevel,
      tags: plant.tags,
    });

    return diagnosis;
  }

  // Sharing methods
  static async shareIdentificationOnly(plant: Plant, imageUri: string, userId: string): Promise<PlantIdentification> {
    const identification = await this.createPlantIdentification({
      user_id: userId,
      image_url: imageUri,
      scientific_name: plant.scientificName,
      common_name: plant.commonName,
      description: plant.description,
      care_instructions: JSON.stringify(plant.careInstructions),
      tags: plant.tags,
      confidence_score: 0.95,
      identification_source: 'openrouter_api',
      is_verified: false,
      is_public: true,
      plant_type: plant.plantType,
      native_region: plant.nativeRegion,
      toxicity_level: plant.toxicityLevel,
      toxicity_warning: plant.toxicityWarning,
      growth_habit: plant.growthHabit,
      growth_rate: plant.growthRate,
      mature_height: plant.matureHeight,
      mature_width: plant.matureWidth,
      mature_description: plant.matureDescription,
      bloom_time: plant.bloomTime,
      flower_colors: plant.flowerColors,
      foliage_type: plant.foliageType,
      hardiness_zones: plant.hardinessZones,
      min_temperature: plant.minTemperature,
      pests_and_diseases: plant.pestsAndDiseases,
      fun_facts: plant.funFacts,
      uses: plant.uses,
      propagation: plant.propagation,
      seasonal_care: plant.seasonalCare,
      companion_plants: plant.companionPlants,
      maintenance_level: plant.maintenanceLevel,
    });

    return identification;
  }
}
