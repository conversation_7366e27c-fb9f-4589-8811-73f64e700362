// COMMENTED OUT FOR PRODUCTION - This is a debug component
/*
import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import Constants from 'expo-constants';
import { Colors } from '@/constants/colors';
import { useAuth } from '@/hooks/useAuth';

interface ConfigDebugProps {
  onClose: () => void;
}

export function ConfigDebug({ onClose }: ConfigDebugProps) {
  const { user, session, isSessionValid } = useAuth();
  const config = {
    // Environment variables
    env: {
      EXPO_PUBLIC_APPWRITE_ENDPOINT: process.env.EXPO_PUBLIC_APPWRITE_ENDPOINT,
      EXPO_PUBLIC_APPWRITE_PROJECT_ID: process.env.EXPO_PUBLIC_APPWRITE_PROJECT_ID,
      OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY,
      OPENROUTER_MODEL: process.env.OPENROUTER_MODEL,
    },
    // Expo config
    expoConfig: {
      appwriteEndpoint: Constants.expoConfig?.extra?.appwriteEndpoint,
      appwriteProjectId: Constants.expoConfig?.extra?.appwriteProjectId,
      openrouterApiKey: Constants.expoConfig?.extra?.openrouterApiKey,
      openrouterModel: Constants.expoConfig?.extra?.openrouterModel,
    },
    // Constants info
    constants: {
      appOwnership: Constants.appOwnership,
      executionEnvironment: Constants.executionEnvironment,
      platform: Constants.platform,
    }
  };

  const maskSensitive = (value: string | undefined): string => {
    if (!value) return 'undefined';
    if (value.length <= 10) return value;
    return `${value.substring(0, 10)}...${value.substring(value.length - 4)}`;
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Configuration Debug</Text>
        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <Text style={styles.closeText}>✕</Text>
        </TouchableOpacity>
      </View>
      
      <ScrollView style={styles.content}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Environment Variables</Text>
          <Text style={styles.item}>EXPO_PUBLIC_APPWRITE_ENDPOINT: {config.env.EXPO_PUBLIC_APPWRITE_ENDPOINT || 'undefined'}</Text>
          <Text style={styles.item}>EXPO_PUBLIC_APPWRITE_PROJECT_ID: {maskSensitive(config.env.EXPO_PUBLIC_APPWRITE_PROJECT_ID)}</Text>
          <Text style={styles.item}>OPENROUTER_API_KEY: {maskSensitive(config.env.OPENROUTER_API_KEY)}</Text>
          <Text style={styles.item}>OPENROUTER_MODEL: {config.env.OPENROUTER_MODEL || 'undefined'}</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Expo Config (app.config.js)</Text>
          <Text style={styles.item}>appwriteEndpoint: {config.expoConfig.appwriteEndpoint || 'undefined'}</Text>
          <Text style={styles.item}>appwriteProjectId: {maskSensitive(config.expoConfig.appwriteProjectId)}</Text>
          <Text style={styles.item}>openrouterApiKey: {maskSensitive(config.expoConfig.openrouterApiKey)}</Text>
          <Text style={styles.item}>openrouterModel: {config.expoConfig.openrouterModel || 'undefined'}</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Runtime Info</Text>
          <Text style={styles.item}>App Ownership: {config.constants.appOwnership}</Text>
          <Text style={styles.item}>Execution Environment: {config.constants.executionEnvironment}</Text>
          <Text style={styles.item}>Platform: {JSON.stringify(config.constants.platform)}</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Configuration Status</Text>
          <Text style={[styles.item, config.env.EXPO_PUBLIC_APPWRITE_ENDPOINT ? styles.success : styles.error]}>
            ✓ Appwrite Endpoint: {config.env.EXPO_PUBLIC_APPWRITE_ENDPOINT ? 'Loaded' : 'Missing'}
          </Text>
          <Text style={[styles.item, config.env.EXPO_PUBLIC_APPWRITE_PROJECT_ID ? styles.success : styles.error]}>
            ✓ Appwrite Project ID: {config.env.EXPO_PUBLIC_APPWRITE_PROJECT_ID ? 'Loaded' : 'Missing'}
          </Text>
          <Text style={[styles.item, config.expoConfig.appwriteEndpoint ? styles.success : styles.error]}>
            ✓ Expo Config Endpoint: {config.expoConfig.appwriteEndpoint ? 'Loaded' : 'Missing'}
          </Text>
          <Text style={[styles.item, config.expoConfig.appwriteProjectId ? styles.success : styles.error]}>
            ✓ Expo Config Project ID: {config.expoConfig.appwriteProjectId ? 'Loaded' : 'Missing'}
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Authentication Status</Text>
          <Text style={[styles.item, user ? styles.success : styles.error]}>
            ✓ User: {user ? 'Authenticated' : 'Not authenticated'}
          </Text>
          <Text style={[styles.item, session ? styles.success : styles.error]}>
            ✓ Session: {session ? 'Active' : 'None'}
          </Text>
          <Text style={[styles.item, isSessionValid() ? styles.success : styles.error]}>
            ✓ Session Valid: {isSessionValid() ? 'Yes' : 'No/Expired'}
          </Text>
          {user && (
            <>
              <Text style={styles.item}>User ID: {user.id}</Text>
              <Text style={styles.item}>Email: {user.email}</Text>
            </>
          )}
          {session && (
            <>
              <Text style={styles.item}>Expires At: {new Date(session.expires_at! * 1000).toLocaleString()}</Text>
              <Text style={styles.item}>Token Type: {session.token_type}</Text>
            </>
          )}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
  },
  closeButton: {
    padding: 8,
  },
  closeText: {
    fontSize: 18,
    color: Colors.textMuted,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 8,
  },
  item: {
    fontSize: 12,
    color: Colors.textMuted,
    marginBottom: 4,
    fontFamily: 'monospace',
  },
  success: {
    color: '#22c55e',
  },
  error: {
    color: '#ef4444',
  },
});
*/

// Placeholder export for production
export function ConfigDebug({ onClose }: { onClose: () => void }) {
  return null;
}
