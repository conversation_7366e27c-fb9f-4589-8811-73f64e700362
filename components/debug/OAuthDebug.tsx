import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView, Alert } from 'react-native';
import { Colors } from '@/constants/colors';
import * as WebBrowser from 'expo-web-browser';

interface OAuthDebugProps {
  onClose: () => void;
}

export function OAuthDebug({ onClose }: OAuthDebugProps) {
  const [logs, setLogs] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
    console.log(`[OAuth Debug] ${message}`);
  };

  const testOAuthFlow = async () => {
    setLoading(true);
    setLogs([]);

    addLog('OAuth testing is currently disabled - migrated to Appwrite');
    setLoading(false);
    return;
    
    try {
      addLog('Starting OAuth test...');
      
      // Test 1: Check Supabase configuration
      addLog('Testing Supabase configuration...');
      const { data: { session } } = await supabase.auth.getSession();
      addLog(`Current session: ${session ? 'Active' : 'None'}`);
      
      // Test 2: Generate OAuth URL
      addLog('Generating OAuth URL...');
      const redirectUrl = 'https://auth.expo.io/@geoattract/app-plantconnects-290725';
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: redirectUrl,
          skipBrowserRedirect: true,
        },
      });

      if (error) {
        addLog(`OAuth URL generation failed: ${error.message}`);
        return;
      }

      addLog(`OAuth URL generated successfully: ${data.url}`);
      
      // Test 3: Parse the OAuth URL to check parameters
      try {
        const url = new URL(data.url);
        addLog(`OAuth URL host: ${url.host}`);
        addLog(`OAuth URL pathname: ${url.pathname}`);
        addLog(`OAuth URL search params:`);
        url.searchParams.forEach((value, key) => {
          if (key === 'redirect_to') {
            addLog(`  ${key}: ${decodeURIComponent(value)}`);
          } else {
            addLog(`  ${key}: ${value}`);
          }
        });
      } catch (urlError) {
        addLog(`Failed to parse OAuth URL: ${urlError}`);
      }

      // Test 4: Test WebBrowser availability
      addLog('Testing WebBrowser availability...');
      if (WebBrowser && typeof WebBrowser.openAuthSessionAsync === 'function') {
        addLog('WebBrowser.openAuthSessionAsync is available');
        
        // Ask user if they want to test the actual OAuth flow
        Alert.alert(
          'Test OAuth Flow',
          'Do you want to test the actual OAuth flow? This will open the browser.',
          [
            { text: 'Cancel', style: 'cancel' },
            { 
              text: 'Test', 
              onPress: async () => {
                try {
                  addLog('Opening OAuth browser...');
                  await WebBrowser.warmUpAsync();
                  
                  const result = await WebBrowser.openAuthSessionAsync(
                    data.url,
                    'plantconnects://auth/callback',
                    {
                      showInRecents: false,
                      createTask: false,
                    }
                  );
                  
                  addLog(`Browser result type: ${result.type}`);
                  if (result.type === 'success') {
                    addLog(`Callback URL: ${result.url}`);
                    
                    // Parse callback URL
                    try {
                      const callbackUrl = new URL(result.url);
                      addLog('Callback URL parameters:');
                      callbackUrl.searchParams.forEach((value, key) => {
                        addLog(`  ${key}: ${key.includes('token') ? '[PRESENT]' : value}`);
                      });
                    } catch (parseError) {
                      addLog(`Failed to parse callback URL: ${parseError}`);
                    }
                  }
                  
                  await WebBrowser.coolDownAsync();
                } catch (browserError) {
                  addLog(`Browser error: ${browserError}`);
                }
              }
            }
          ]
        );
      } else {
        addLog('WebBrowser.openAuthSessionAsync is NOT available');
      }
      
    } catch (error) {
      addLog(`Test failed: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>OAuth Debug Tool</Text>
        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <Text style={styles.closeButtonText}>×</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.controls}>
        <TouchableOpacity
          onPress={testOAuthFlow}
          disabled={loading}
          style={[styles.button, loading && styles.buttonDisabled]}
        >
          <Text style={styles.buttonText}>
            {loading ? 'Testing...' : 'Test OAuth Flow'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity onPress={clearLogs} style={styles.clearButton}>
          <Text style={styles.clearButtonText}>Clear Logs</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.logsContainer}>
        {logs.map((log, index) => (
          <Text key={index} style={styles.logText}>
            {log}
          </Text>
        ))}
        {logs.length === 0 && (
          <Text style={styles.placeholderText}>
            Click "Test OAuth Flow" to start debugging
          </Text>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.text,
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.cardBackground,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 20,
    color: Colors.text,
  },
  controls: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  button: {
    flex: 1,
    backgroundColor: Colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  buttonText: {
    color: Colors.background,
    fontWeight: '600',
  },
  clearButton: {
    backgroundColor: Colors.cardBackground,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  clearButtonText: {
    color: Colors.text,
  },
  logsContainer: {
    flex: 1,
    backgroundColor: Colors.cardBackground,
    borderRadius: 8,
    padding: 12,
  },
  logText: {
    fontSize: 12,
    color: Colors.text,
    marginBottom: 4,
    fontFamily: 'monospace',
  },
  placeholderText: {
    fontSize: 14,
    color: Colors.textMuted,
    textAlign: 'center',
    marginTop: 20,
  },
});
