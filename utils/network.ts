/**
 * Network utility functions for checking connectivity and handling network errors
 */

/**
 * Check if the device has network connectivity
 */
export const checkNetworkConnectivity = async (): Promise<boolean> => {
  try {
    // Try to fetch a small resource from a reliable endpoint to test connectivity
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);

    const response = await fetch('https://www.google.com/', {
      method: 'HEAD',
      signal: controller.signal,
    });
    
    clearTimeout(timeoutId);
    return response.ok;
  } catch (error) {
    console.error('Network connectivity check failed:', error);
    return false;
  }
};

/**
 * Check Appwrite storage connectivity (placeholder - implement as needed)
 */
export const checkAppwriteStorageConnectivity = async (): Promise<{
  connected: boolean;
  authenticated: boolean;
  error?: string;
}> => {
  try {
    // For now, just return true - can be implemented with actual Appwrite storage checks
    return {
      connected: true,
      authenticated: true,
    };
  } catch (error) {
    return {
      connected: false,
      authenticated: false,
      error: error instanceof Error ? error.message : 'Unknown connectivity error',
    };
  }
    
  } catch (error) {
    console.error('Supabase storage connectivity check failed:', error);
    return {
      connected: false,
      authenticated: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

/**
 * Check if an error is network-related
 */
export const isNetworkError = (error: any): boolean => {
  if (!error) return false;
  
  const errorMessage = error.message?.toLowerCase() || '';
  const errorString = error.toString?.()?.toLowerCase() || '';
  
  const networkErrorIndicators = [
    'network',
    'fetch',
    'connection',
    'timeout',
    'offline',
    'no internet',
    'dns',
    'unreachable',
    'failed to fetch',
    'network request failed',
    'connection refused',
    'connection timeout'
  ];
  
  return networkErrorIndicators.some(indicator => 
    errorMessage.includes(indicator) || errorString.includes(indicator)
  );
};

/**
 * Get a user-friendly error message for network errors
 */
export const getNetworkErrorMessage = (error: any): string => {
  if (isNetworkError(error)) {
    return 'Network connection error. Please check your internet connection and try again.';
  }
  
  // Return original error message if not network-related
  return error instanceof Error ? error.message : 'An unexpected error occurred';
};

/**
 * Retry a function with exponential backoff
 */
export const retryWithBackoff = async <T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> => {
  let lastError: any;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      // Don't retry if it's not a network error
      if (!isNetworkError(error)) {
        throw error;
      }
      
      // Don't retry on the last attempt
      if (attempt === maxRetries) {
        break;
      }
      
      // Wait with exponential backoff
      const delay = baseDelay * Math.pow(2, attempt);
      console.log(`Retrying in ${delay}ms (attempt ${attempt + 1}/${maxRetries + 1})`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError;
};
