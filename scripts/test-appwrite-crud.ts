#!/usr/bin/env node

/**
 * Comprehensive test script for Appwrite CRUD operations
 * Tests all database operations to ensure migration is complete
 */

import { AppwriteDatabaseService } from '../services/appwriteDatabase';
import { account } from '../lib/appwrite';

interface TestResult {
  name: string;
  success: boolean;
  error?: string;
  details?: any;
}

class AppwriteCRUDTester {
  private results: TestResult[] = [];
  private testUserId: string = '';

  async runAllTests(): Promise<void> {
    console.log('🧪 Starting Appwrite CRUD Tests\n');

    try {
      // Setup test user
      await this.setupTestUser();

      // Run all tests
      await this.testUserProfileCRUD();
      await this.testPlantIdentificationCRUD();
      await this.testPlantDiagnosisCRUD();
      await this.testGardenCollectionCRUD();
      await this.testRecoveryTrackingCRUD();

      // Cleanup
      await this.cleanup();

      // Print results
      this.printResults();

    } catch (error) {
      console.error('💥 Test suite failed:', error);
    }
  }

  private async setupTestUser(): Promise<void> {
    try {
      // Try to get current user or create a test session
      try {
        const user = await account.get();
        this.testUserId = user.$id;
        console.log(`✅ Using existing user: ${this.testUserId}\n`);
      } catch (error) {
        // No active session, create a test user ID
        this.testUserId = 'test-user-' + Date.now();
        console.log(`✅ Using test user ID: ${this.testUserId}\n`);
      }
    } catch (error) {
      throw new Error(`Failed to setup test user: ${error}`);
    }
  }

  private async testUserProfileCRUD(): Promise<void> {
    console.log('1️⃣ Testing User Profile CRUD...');

    // Test Create
    try {
      const profileData = {
        user_id: this.testUserId,
        username: 'test_user',
        display_name: 'Test User',
        bio: 'Test bio',
        is_public: false,
        allow_garden_sharing: false,
        allow_profile_indexing: false,
        experience_level: 'beginner' as const,
        total_identifications: 0,
        total_diagnoses: 0,
        community_points: 0,
        achievements: [],
      };

      const createdProfile = await AppwriteDatabaseService.createUserProfile(profileData);
      this.addResult('User Profile Create', !!createdProfile, undefined, { id: createdProfile?.id });

      if (createdProfile) {
        // Test Read
        const readProfile = await AppwriteDatabaseService.getUserProfile(this.testUserId);
        this.addResult('User Profile Read', !!readProfile, undefined, { found: !!readProfile });

        // Test Update
        const updatedProfile = await AppwriteDatabaseService.updateUserProfile(this.testUserId, {
          bio: 'Updated test bio',
          community_points: 100,
        });
        this.addResult('User Profile Update', !!updatedProfile, undefined, { updated: !!updatedProfile });

        // Test Stats Update
        const statsUpdated = await AppwriteDatabaseService.updateUserProfileStats(this.testUserId);
        this.addResult('User Profile Stats Update', !!statsUpdated, undefined, { statsUpdated: !!statsUpdated });
      }
    } catch (error: any) {
      this.addResult('User Profile CRUD', false, error.message);
    }
  }

  private async testPlantIdentificationCRUD(): Promise<void> {
    console.log('2️⃣ Testing Plant Identification CRUD...');

    try {
      const identificationData = {
        user_id: this.testUserId,
        image_url: 'https://example.com/test-plant.jpg',
        scientific_name: 'Monstera deliciosa',
        common_name: 'Swiss Cheese Plant',
        description: 'A popular houseplant with distinctive split leaves',
        care_instructions: JSON.stringify({
          light: 'bright indirect',
          water: 'moderate',
          humidity: 'high'
        }),
        tags: ['houseplant', 'tropical'],
        confidence_score: 0.95,
        identification_source: 'test',
        is_verified: false,
        is_public: true,
        plant_type: 'Houseplant',
        native_region: 'Central America',
        toxicity_level: 'mild',
        toxicity_warning: 'Mildly toxic to pets',
        growth_habit: 'Climbing',
        growth_rate: 'Fast',
        mature_height: '6-8 feet',
        mature_width: '3-4 feet',
        mature_description: 'Large climbing plant',
        bloom_time: 'Rarely indoors',
        flower_colors: [],
        foliage_type: 'Evergreen',
        hardiness_zones: '10-12',
        min_temperature: '65°F',
        pests_and_diseases: 'Spider mites, scale',
        fun_facts: ['Can grow aerial roots'],
        uses: ['Ornamental', 'Air purifier'],
        propagation: 'Stem cuttings',
        seasonal_care: 'Reduce watering in winter',
        companion_plants: ['Pothos', 'Philodendron'],
        maintenance_level: 'Medium',
        notes: 'Test plant identification',
        nickname: 'Test Monstera',
        health_status: 'healthy',
        location_in_garden: 'Living room',
        allow_community_tips: true,
      };

      // Test Create
      const createdIdentification = await AppwriteDatabaseService.createPlantIdentification(identificationData);
      this.addResult('Plant Identification Create', !!createdIdentification, undefined, { id: createdIdentification?.id });

      if (createdIdentification) {
        // Test Read
        const readIdentification = await AppwriteDatabaseService.getPlantIdentification(createdIdentification.id);
        this.addResult('Plant Identification Read', !!readIdentification);

        // Test List Recent
        const recentIdentifications = await AppwriteDatabaseService.getRecentPlantIdentifications(this.testUserId, 5);
        this.addResult('Plant Identification List Recent', Array.isArray(recentIdentifications), undefined, { count: recentIdentifications.length });

        // Test Update
        const updatedIdentification = await AppwriteDatabaseService.updatePlantIdentification(createdIdentification.id, {
          notes: 'Updated test notes',
          nickname: 'Updated Monstera',
        });
        this.addResult('Plant Identification Update', !!updatedIdentification);

        // Test Delete
        const deleted = await AppwriteDatabaseService.removePlantIdentification(createdIdentification.id);
        this.addResult('Plant Identification Delete', deleted);
      }
    } catch (error: any) {
      this.addResult('Plant Identification CRUD', false, error.message);
    }
  }

  private async testPlantDiagnosisCRUD(): Promise<void> {
    console.log('3️⃣ Testing Plant Diagnosis CRUD...');

    try {
      const diagnosisData = {
        user_id: this.testUserId,
        image_url: 'https://example.com/test-diagnosis.jpg',
        problem_description: 'Leaves turning yellow',
        diagnosed_problem: 'Overwatering',
        likely_causes: ['Too much water', 'Poor drainage'],
        symptoms_observed: 'Yellow leaves, soft stems',
        severity: 'moderate' as const,
        immediate_actions: ['Reduce watering', 'Check drainage'],
        long_term_care: ['Improve soil drainage', 'Monitor watering schedule'],
        product_recommendations: ['Well-draining potting mix'],
        step_by_step_instructions: ['Stop watering', 'Check roots', 'Repot if needed'],
        prevention_tips: ['Water only when soil is dry', 'Use well-draining soil'],
        prognosis: 'Good with proper care',
        confidence_score: 0.85,
        diagnosis_source: 'test',
        is_verified: false,
        is_public: false,
        notes: 'Test diagnosis',
        location: 'Indoor',
        nickname: 'Test Plant',
        health_status: 'recovering',
        allow_community_tips: false,
        scientific_name: 'Test plantus',
        common_name: 'Test Plant',
        description: 'A test plant for diagnosis',
        care_instructions: JSON.stringify({ water: 'low' }),
        plant_type: 'Test',
        native_region: 'Test Region',
        toxicity_level: 'none',
        toxicity_warning: 'Non-toxic',
        growth_habit: 'Upright',
        growth_rate: 'Medium',
        mature_height: '1-2 feet',
        mature_width: '1 foot',
        mature_description: 'Small test plant',
        bloom_time: 'Never',
        flower_colors: [],
        foliage_type: 'Evergreen',
        hardiness_zones: '1-12',
        min_temperature: '32°F',
        pests_and_diseases: 'None',
        fun_facts: ['It is a test'],
        uses: ['Testing'],
        propagation: 'Division',
        seasonal_care: 'Same year round',
        companion_plants: ['Other test plants'],
        maintenance_level: 'Low',
      };

      // Test Create
      const createdDiagnosis = await AppwriteDatabaseService.createPlantDiagnosis(diagnosisData);
      this.addResult('Plant Diagnosis Create', !!createdDiagnosis, undefined, { id: createdDiagnosis?.id });

      if (createdDiagnosis) {
        // Test Read
        const readDiagnosis = await AppwriteDatabaseService.getPlantDiagnosis(createdDiagnosis.id);
        this.addResult('Plant Diagnosis Read', !!readDiagnosis);

        // Test List Recent
        const recentDiagnoses = await AppwriteDatabaseService.getRecentPlantDiagnoses(this.testUserId, 5);
        this.addResult('Plant Diagnosis List Recent', Array.isArray(recentDiagnoses), undefined, { count: recentDiagnoses.length });

        // Test Update
        const updatedDiagnosis = await AppwriteDatabaseService.updatePlantDiagnosis(createdDiagnosis.id, {
          notes: 'Updated test notes',
          severity: 'mild',
        });
        this.addResult('Plant Diagnosis Update', !!updatedDiagnosis);

        // Test Delete
        const deleted = await AppwriteDatabaseService.removePlantDiagnosis(createdDiagnosis.id);
        this.addResult('Plant Diagnosis Delete', deleted);
      }
    } catch (error: any) {
      this.addResult('Plant Diagnosis CRUD', false, error.message);
    }
  }

  private async testGardenCollectionCRUD(): Promise<void> {
    console.log('4️⃣ Testing Garden Collection CRUD...');

    try {
      const gardenData = {
        user_id: this.testUserId,
        plant_identification_id: 'test-plant-id',
        nickname: 'My Test Plant',
        location_in_garden: 'Living Room',
        notes: 'Test garden plant',
        health_status: 'healthy' as const,
        is_favorite: true,
        allow_community_tips: false,
      };

      // Test Create
      const createdGarden = await AppwriteDatabaseService.addToGarden(gardenData);
      this.addResult('Garden Collection Create', !!createdGarden, undefined, { id: createdGarden?.id });

      if (createdGarden) {
        // Test Read
        const userGarden = await AppwriteDatabaseService.getUserGarden(this.testUserId);
        this.addResult('Garden Collection Read', Array.isArray(userGarden), undefined, { count: userGarden.length });

        // Test Update
        const updatedGarden = await AppwriteDatabaseService.updateGardenItem(createdGarden.id, {
          notes: 'Updated garden notes',
          health_status: 'needs_attention',
        });
        this.addResult('Garden Collection Update', !!updatedGarden);

        // Test Delete
        const deleted = await AppwriteDatabaseService.removeFromGarden(createdGarden.id);
        this.addResult('Garden Collection Delete', deleted);
      }
    } catch (error: any) {
      this.addResult('Garden Collection CRUD', false, error.message);
    }
  }

  private async testRecoveryTrackingCRUD(): Promise<void> {
    console.log('5️⃣ Testing Recovery Tracking CRUD...');

    try {
      const trackingData = {
        user_id: this.testUserId,
        plant_diagnosis_id: 'test-diagnosis-id',
        recovery_stage: 'treatment_started' as const,
        progress_notes: 'Started treatment',
        image_url: 'https://example.com/recovery.jpg',
        health_score: 6,
        actions_taken: ['Reduced watering'],
        next_steps: ['Monitor for improvement'],
        is_milestone: true,
      };

      // Test Create
      const createdTracking = await AppwriteDatabaseService.createRecoveryTracking(trackingData);
      this.addResult('Recovery Tracking Create', !!createdTracking, undefined, { id: createdTracking?.id });

      if (createdTracking) {
        // Test Read
        const recoveryHistory = await AppwriteDatabaseService.getRecoveryHistory('test-diagnosis-id');
        this.addResult('Recovery Tracking Read', Array.isArray(recoveryHistory), undefined, { count: recoveryHistory.length });

        // Test Update
        const updatedTracking = await AppwriteDatabaseService.updateRecoveryTracking(createdTracking.id, {
          progress_notes: 'Updated progress notes',
          health_score: 8,
        });
        this.addResult('Recovery Tracking Update', !!updatedTracking);

        // Test Delete
        const deleted = await AppwriteDatabaseService.removeRecoveryTracking(createdTracking.id);
        this.addResult('Recovery Tracking Delete', deleted);
      }
    } catch (error: any) {
      this.addResult('Recovery Tracking CRUD', false, error.message);
    }
  }

  private async cleanup(): Promise<void> {
    console.log('🧹 Cleaning up test data...');
    
    try {
      // Clean up any remaining test data
      // Note: Most test data should already be deleted by individual tests
      console.log('✅ Cleanup completed');
    } catch (error) {
      console.warn('⚠️ Cleanup had some issues:', error);
    }
  }

  private addResult(name: string, success: boolean, error?: string, details?: any): void {
    this.results.push({ name, success, error, details });
    const status = success ? '✅' : '❌';
    console.log(`  ${status} ${name}${error ? ` - ${error}` : ''}`);
  }

  private printResults(): void {
    console.log('\n📊 Test Results Summary:');
    console.log('=' .repeat(50));
    
    const passed = this.results.filter(r => r.success).length;
    const failed = this.results.filter(r => !r.success).length;
    
    console.log(`Total Tests: ${this.results.length}`);
    console.log(`Passed: ${passed}`);
    console.log(`Failed: ${failed}`);
    console.log(`Success Rate: ${((passed / this.results.length) * 100).toFixed(1)}%`);
    
    if (failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.results.filter(r => !r.success).forEach(result => {
        console.log(`  - ${result.name}: ${result.error}`);
      });
    }
    
    console.log('\n' + (failed === 0 ? '🎉 All tests passed!' : '⚠️ Some tests failed. Check the errors above.'));
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const tester = new AppwriteCRUDTester();
  tester.runAllTests().catch(console.error);
}

export { AppwriteCRUDTester };
