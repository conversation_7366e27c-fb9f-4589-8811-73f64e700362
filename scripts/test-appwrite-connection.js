#!/usr/bin/env node

/**
 * Simple test script to verify Appwrite connection and basic operations
 * Run with: node scripts/test-appwrite-connection.js
 */

const { Client, Databases, Account } = require('appwrite');
require('dotenv').config({ path: '.env.local' });

const appwriteEndpoint = process.env.EXPO_PUBLIC_APPWRITE_ENDPOINT;
const appwriteProjectId = process.env.EXPO_PUBLIC_APPWRITE_PROJECT_ID;

if (!appwriteEndpoint || !appwriteProjectId) {
  console.error('❌ Missing Appwrite environment variables');
  console.log('EXPO_PUBLIC_APPWRITE_ENDPOINT:', appwriteEndpoint ? '✅ Set' : '❌ Missing');
  console.log('EXPO_PUBLIC_APPWRITE_PROJECT_ID:', appwriteProjectId ? '✅ Set' : '❌ Missing');
  process.exit(1);
}

const client = new Client()
  .setEndpoint(appwriteEndpoint)
  .setProject(appwriteProjectId);

const databases = new Databases(client);
const account = new Account(client);

async function testAppwriteConnection() {
  console.log('🧪 Testing Appwrite Connection\n');

  try {
    // Test 1: Check project health by trying to access a collection
    console.log('1️⃣ Testing project connection...');
    try {
      // Try to list documents in a known collection
      const documents = await databases.listDocuments('public', 'user_profiles');
      console.log('✅ Project connection successful');
      console.log(`   user_profiles collection contains ${documents.total} document(s)`);
    } catch (error) {
      if (error.message.includes('Collection with the requested ID could not be found')) {
        console.log('✅ Project connection successful (collection not found is expected)');
      } else {
        console.error('❌ Project connection failed:', error.message);
        return;
      }
    }

    // Test 2: Test individual collections
    console.log('\n2️⃣ Testing individual collections...');
    const testCollections = ['user_profiles', 'plant_identifications'];

    for (const collectionId of testCollections) {
      try {
        const documents = await databases.listDocuments('public', collectionId);
        console.log(`✅ Collection '${collectionId}' accessible - ${documents.total} document(s)`);
      } catch (error) {
        if (error.message.includes('Collection with the requested ID could not be found')) {
          console.log(`⚠️  Collection '${collectionId}' not found (needs to be created)`);
        } else {
          console.log(`❌ Collection '${collectionId}' error: ${error.message}`);
        }
      }
    }

    // Test 3: Test authentication (optional)
    console.log('\n3️⃣ Testing authentication...');
    try {
      const user = await account.get();
      console.log(`✅ Authenticated as: ${user.name} (${user.email})`);
    } catch (error) {
      console.log('ℹ️  No active session (this is normal for server-side testing)');
    }

    console.log('\n🎉 Appwrite connection test completed!');
    console.log('\n📋 Summary:');
    console.log('- Project connection: ✅ Working');
    console.log('- Database access: ✅ Working');
    console.log('- Collections: Check individual results above');
    
  } catch (error) {
    console.error('💥 Test failed with error:', error);
  }
}

// Run the test
if (require.main === module) {
  testAppwriteConnection().catch(console.error);
}

module.exports = { testAppwriteConnection };
