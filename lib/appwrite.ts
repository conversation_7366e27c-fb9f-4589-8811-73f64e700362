import { Client, Databases, Account, Storage, ID } from 'appwrite';
import Constants from 'expo-constants';

const appwriteEndpoint = Constants.expoConfig?.extra?.appwriteEndpoint || process.env.EXPO_PUBLIC_APPWRITE_ENDPOINT;
const appwriteProjectId = Constants.expoConfig?.extra?.appwriteProjectId || process.env.EXPO_PUBLIC_APPWRITE_PROJECT_ID;
const appwriteStorageBucketId = Constants.expoConfig?.extra?.appwriteStorageBucketId || process.env.EXPO_PUBLIC_APPWRITE_STORAGE_BUCKET_ID;

if (!appwriteEndpoint || !appwriteProjectId) {
  throw new Error('Missing Appwrite environment variables. Please check your .env.local file.');
}

// Validate URL format
try {
  new URL(appwriteEndpoint);
} catch (error) {
  throw new Error('Invalid Appwrite endpoint URL format. Please check your configuration.');
}

export const client = new Client()
  .setEndpoint(appwriteEndpoint)
  .setProject(appwriteProjectId);

export const databases = new Databases(client);
export const account = new Account(client);
export const storage = new Storage(client);

// Export constants for easy access
export const DATABASE_ID = 'public'; // Using the public database from Appwrite
export const STORAGE_BUCKET_ID = appwriteStorageBucketId || '';

// Collection IDs - these match the Appwrite collections
export const COLLECTIONS = {
  USER_PROFILES: 'user_profiles',
  PLANT_IDENTIFICATIONS: 'plant_identifications',
  GARDEN_COLLECTIONS: 'garden_collections',
  PLANT_DIAGNOSES: 'plant_diagnoses',
  RECOVERY_TRACKING: 'recovery_tracking',
  USER_ACHIEVEMENTS: 'user_achievements',
  COMMUNITY_POSTS: 'community_posts',
};

// Note: Connection testing is handled by the useAuth hook when needed
// No initialization calls should be made here to avoid "Failed to fetch" errors

export { ID };
